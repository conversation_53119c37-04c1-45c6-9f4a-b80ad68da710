#!/bin/bash

# Quick plugin update script for development
# This script rebuilds and updates the plugin without full reinstallation

set -e

PLUGIN_SOURCE_DIR="."
PLUGIN_TARGET_DIR="/Users/<USER>/Documents/Obsidian/Main/.obsidian/plugins/ghost-sync"

echo "🔄 Updating Ghost Sync Plugin..."

# Check if source directory exists
if [ ! -d "$PLUGIN_SOURCE_DIR" ]; then
    echo "❌ Error: Plugin source directory '$PLUGIN_SOURCE_DIR' not found"
    exit 1
fi

# Check if target directory exists
if [ ! -d "$PLUGIN_TARGET_DIR" ]; then
    echo "❌ Error: Plugin not installed. Run ./install-plugin.sh first"
    exit 1
fi

# Test and build the plugin in source directory
echo "🧪 Running tests..."
cd "$PLUGIN_SOURCE_DIR"
npm run test:ci

echo "🔨 Building plugin..."
npm run build

# Check if build was successful
if [ ! -f "main.js" ]; then
    echo "❌ Error: Build failed - main.js not generated"
    exit 1
fi

# Copy updated files
echo "📋 Copying updated files..."
cp main.js "$PLUGIN_TARGET_DIR/"
cp manifest.json "$PLUGIN_TARGET_DIR/"
cp styles.css "$PLUGIN_TARGET_DIR/"

echo "✅ Plugin updated successfully!"
echo ""
echo "🔄 Next steps:"
echo "1. Reload the plugin in Obsidian (Ctrl+Shift+P → 'Reload app without saving')"
echo "2. Or restart Obsidian to pick up changes"
echo ""
echo "📍 Plugin location: $PLUGIN_TARGET_DIR"
